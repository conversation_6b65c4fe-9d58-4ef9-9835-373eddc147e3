{"name": "mesl-website", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --port 3000 --host", "build": "tsc && vite build", "build:dev": "tsc && vite build --mode development", "build:analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview --port 3000 --host", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "clean": "rimraf node_modules dist .next", "clean:cache": "rimraf node_modules/.cache .eslintcache", "start": "vite --port 3000 --host", "test": "echo \"No tests specified\" && exit 0", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "prepare": "husky install || true"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@tanstack/react-query": "^5.56.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.23.6", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.7.4", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^7.0.5"}}